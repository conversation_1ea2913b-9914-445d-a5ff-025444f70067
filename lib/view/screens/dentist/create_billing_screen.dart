import 'package:flutter/material.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/widgets/custom_dropdown_button.dart';

enum BillingScreenMode { create, edit, view }

class CreateBillingScreen extends StatefulWidget {
  final bool showSearchOption;
  final BillingScreenMode mode;
  final String? patientName;
  final String? serviceName;
  final String? amount;
  final String? date;

  const CreateBillingScreen({
    super.key,
    this.showSearchOption = true,
    this.mode = BillingScreenMode.create,
    this.patientName,
    this.serviceName,
    this.amount,
    this.date,
  });

  @override
  State<CreateBillingScreen> createState() => _CreateBillingScreenState();
}

class _CreateBillingScreenState extends State<CreateBillingScreen> {
  String? _selectedPaymentSource;
  final PermissionService _permissionService = PermissionService();

  // Text controllers for form fields
  late TextEditingController searchPatientController;
  late TextEditingController searchServiceController;
  late TextEditingController priceController;
  late TextEditingController discountController;
  late TextEditingController discountAmountController;
  late TextEditingController totalAmountController;
  late TextEditingController paidAmountController;
  late TextEditingController balanceAmountController;

  bool get _isReadOnly => widget.mode == BillingScreenMode.view;

  String get _getAppBarTitle {
    switch (widget.mode) {
      case BillingScreenMode.create:
        return 'Create Billing';
      case BillingScreenMode.edit:
        return 'Edit Billing';
      case BillingScreenMode.view:
        return 'View Billing';
    }
  }

  @override
  void initState() {
    super.initState();
    searchPatientController = TextEditingController(text: widget.patientName ?? '');
    searchServiceController = TextEditingController(text: widget.serviceName ?? '');
    priceController = TextEditingController();
    discountController = TextEditingController();
    discountAmountController = TextEditingController();
    totalAmountController = TextEditingController();
    paidAmountController = TextEditingController();
    balanceAmountController = TextEditingController();

    // Set amount if provided
    if (widget.amount != null) {
      final cleanAmount = widget.amount!.replaceAll('₹', '').replaceAll(',', '');
      totalAmountController.text = cleanAmount;
      paidAmountController.text = cleanAmount;
    }
  }

  @override
  void dispose() {
    searchPatientController.dispose();
    searchServiceController.dispose();
    priceController.dispose();
    discountController.dispose();
    discountAmountController.dispose();
    totalAmountController.dispose();
    paidAmountController.dispose();
    balanceAmountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getAppBarTitle),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.showSearchOption)
                Visibility(
                  visible: _permissionService.hasPermission('billing', 'is_list'),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'Search Patient',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          const Text(
                            ' *',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      CustomTextFormField(
                        controller: searchPatientController,
                        hintText: 'Search Patient',
                        prefix: const Icon(Icons.search),
                        enabled: !_isReadOnly,
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              const Text(
                'First Name',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter First Name',
              ),
              const SizedBox(height: 20),
              const Text(
                'Last Name',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter Last Name',
              ),
              const SizedBox(height: 20),
              const Text(
                'Patient ID',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter Patient ID',
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Age',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        const CustomTextFormField(
                          hintText: '10',
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Date Of Birth',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        const CustomTextFormField(
                          hintText: '09 Feb 2021',
                          prefix: Icon(Icons.calendar_today),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const Text(
                'Gender',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              Row(
                children: [
                  Radio(
                    value: 'Male',
                    groupValue: 'Male',
                    onChanged: (value) {},
                    activeColor: AppColors.primary,
                  ),
                  const Text('Male'),
                  Radio(
                    value: 'Female',
                    groupValue: 'Male',
                    onChanged: (value) {},
                    activeColor: AppColors.primary,
                  ),
                  const Text('Female'),
                ],
              ),
              const SizedBox(height: 20),
              Visibility(
                visible: _permissionService.hasPermission('billing', 'is_list'),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text(
                          'Search Service',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const Text(
                          ' *',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    CustomTextFormField(
                      controller: searchServiceController,
                      hintText: 'Search Service Name',
                      prefix: const Icon(Icons.search),
                      enabled: !_isReadOnly,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              const Text(
                'Price',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: priceController,
                hintText: 'Enter Price',
                enabled: !_isReadOnly,
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Discount (%)',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        CustomTextFormField(
                          controller: discountController,
                          hintText: '10',
                          enabled: !_isReadOnly,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Discount (Amount)',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        CustomTextFormField(
                          controller: discountAmountController,
                          hintText: '10',
                          enabled: !_isReadOnly,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const Text(
                'Total Amount',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: totalAmountController,
                hintText: 'Enter Total Amount',
                enabled: !_isReadOnly,
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  const Text(
                    'Paid Amount',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const Text(
                    ' *',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: paidAmountController,
                hintText: 'Enter Paid Amount',
                enabled: !_isReadOnly,
              ),
              const SizedBox(height: 20),
              const Text(
                'Balance Amount',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: balanceAmountController,
                hintText: 'Enter Balance Amount',
                enabled: !_isReadOnly,
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  const Text(
                    'Payment Source',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const Text(
                    ' *',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              CustomDropdownButton(
                hintText: 'Select Payment Source',
                items: [
                  DropdownMenuItem(
                    value: 'UPI',
                    child: Row(
                      children: [
                        Icon(Icons.payment),
                        SizedBox(width: 8),
                        Text('UPI'),
                      ],
                    ),
                  ),
                  DropdownMenuItem(
                    value: 'Card',
                    child: Row(
                      children: [
                        Icon(Icons.credit_card),
                        SizedBox(width: 8),
                        Text('Card'),
                      ],
                    ),
                  ),
                  DropdownMenuItem(
                    value: 'Cash',
                    child: Row(
                      children: [
                        Icon(Icons.money),
                        SizedBox(width: 8),
                        Text('Cash'),
                      ],
                    ),
                  ),
                ],
                value: _selectedPaymentSource,
                onChanged: _isReadOnly ? null : (newValue) {
                  setState(() {
                    _selectedPaymentSource = newValue;
                  });
                },
              ),
              const SizedBox(height: 30),
              if (widget.mode != BillingScreenMode.view)
                CustomElevatedButton(
                  onPressed: () {},
                  text: widget.mode == BillingScreenMode.edit ? 'Update' : 'Save',
                  buttonStyle: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
