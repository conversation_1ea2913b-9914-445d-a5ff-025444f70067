import 'package:flutter/material.dart';
import 'package:platix/utils/app_export.dart';

enum ReminderScreenMode { create, edit, view }

class CreateReminderScreen extends StatefulWidget {
  final bool showSearchOption;
  final ReminderScreenMode mode;
  final String? firstName;
  final String? lastName;
  final String? mobileNumber;
  final String? reminderTime;
  final String? reminderDate;
  final String? reminderText;

  const CreateReminderScreen({
    super.key,
    this.showSearchOption = true,
    this.mode = ReminderScreenMode.create,
    this.firstName,
    this.lastName,
    this.mobileNumber,
    this.reminderTime,
    this.reminderDate,
    this.reminderText,
  });

  @override
  State<CreateReminderScreen> createState() => _CreateReminderScreenState();
}

class _CreateReminderScreenState extends State<CreateReminderScreen> {
  late TextEditingController searchController;
  late TextEditingController firstNameController;
  late TextEditingController lastNameController;
  late TextEditingController mobileController;
  late TextEditingController timeController;
  late TextEditingController dateController;
  late TextEditingController reminderTextController;

  @override
  void initState() {
    super.initState();
    searchController = TextEditingController();
    firstNameController = TextEditingController(text: widget.firstName ?? '');
    lastNameController = TextEditingController(text: widget.lastName ?? '');
    mobileController = TextEditingController(text: widget.mobileNumber ?? '');
    timeController = TextEditingController(text: widget.reminderTime ?? '');
    dateController = TextEditingController(text: widget.reminderDate ?? '');
    reminderTextController = TextEditingController(text: widget.reminderText ?? '');
  }

  @override
  void dispose() {
    searchController.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    mobileController.dispose();
    timeController.dispose();
    dateController.dispose();
    reminderTextController.dispose();
    super.dispose();
  }

  String get _getAppBarTitle {
    switch (widget.mode) {
      case ReminderScreenMode.create:
        return 'Create Reminder';
      case ReminderScreenMode.edit:
        return 'Edit Reminder';
      case ReminderScreenMode.view:
        return 'View Reminder';
    }
  }

  bool get _isReadOnly => widget.mode == ReminderScreenMode.view;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getAppBarTitle),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.showSearchOption) ...[
                const TextFieldLabel(
                  label: 'Search Patient',
                  isMandatory: true,
                ),
                CustomTextFormField(
                  controller: searchController,
                  hintText: 'Search Patient',
                  prefix: const Icon(Icons.search),
                  enabled: !_isReadOnly,
                ),
                const SizedBox(height: 20),
              ],
              const TextFieldLabel(
                label: 'First Name',
                isMandatory: false,
              ),
              CustomTextFormField(
                controller: firstNameController,
                hintText: 'Enter First Name',
                enabled: !_isReadOnly,
              ),
              const SizedBox(height: 20),
              const TextFieldLabel(
                label: 'Last Name',
                isMandatory: false,
              ),
              CustomTextFormField(
                controller: lastNameController,
                hintText: 'Enter Last Name',
                enabled: !_isReadOnly,
              ),
              const SizedBox(height: 20),
              const TextFieldLabel(
                label: 'Mobile Number',
                isMandatory: false,
              ),
              CustomTextFormField(
                controller: mobileController,
                hintText: '+91 XXXXXXXXXX',
                prefix: const Icon(Icons.phone),
                enabled: !_isReadOnly,
              ),
              const SizedBox(height: 20),
              const TextFieldLabel(
                label: 'Select Reminder',
                isMandatory: true,
              ),
              Row(
                children: [
                  Expanded(
                    child: CustomTextFormField(
                      controller: timeController,
                      hintText: '10:24 AM',
                      prefix: const Icon(Icons.access_time),
                      enabled: !_isReadOnly,
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: CustomTextFormField(
                      controller: dateController,
                      hintText: '09 Feb 2021',
                      prefix: const Icon(Icons.calendar_today),
                      enabled: !_isReadOnly,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const TextFieldLabel(
                label: 'Reminder Text',
                isMandatory: true,
              ),
              CustomTextFormField(
                controller: reminderTextController,
                hintText: 'what should I remind you on ?',
                maxLines: 5,
                enabled: !_isReadOnly,
              ),
              const SizedBox(height: 30),
              if (widget.mode != ReminderScreenMode.view)
                CustomElevatedButton(
                  onPressed: () {},
                  text: widget.mode == ReminderScreenMode.edit ? 'Update' : 'Save',
                ),
            ],
          ),
        ),
      ),
    );
  }
}
