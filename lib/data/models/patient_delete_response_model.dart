class PatientDeleteResponse {
  final String message;
  final String? error;
  final String? details;

  PatientDeleteResponse({
    required this.message,
    this.error,
    this.details,
  });

  factory PatientDeleteResponse.fromJson(Map<String, dynamic> json) {
    return PatientDeleteResponse(
      message: json['message'] ?? '',
      error: json['error'],
      details: json['details'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'error': error,
      'details': details,
    };
  }

  // Helper method to check if the operation was successful
  bool get isSuccess => error == null;
}