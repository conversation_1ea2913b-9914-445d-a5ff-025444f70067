class BillingModel {
  final bool status;
  final BillingData? data;
  final String? message;

  BillingModel({
    required this.status,
    this.data,
    this.message,
  });

  factory BillingModel.fromJson(Map<String, dynamic> json) {
    return BillingModel(
      status: json['status'] ?? false,
      data: json['data'] != null ? BillingData.fromJson(json['data']) : null,
      message: json['message']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'data': data?.toJson(),
      'message': message,
    };
  }
}

class BillingData {
  final String? id;
  final String? patientRegId;
  final String? patientId;
  final String? firstName;
  final String? lastName;
  final String? mobile;
  final String? gender;
  final int? age;
  final List<BillingService> services;
  final String? discountPercent;
  final String? discountAmount;
  final String? totalAmount;
  final String? paidAmount;
  final String? balanceAmount;
  final String? paymentSource;
  final String? createdAt;
  final String? updatedAt;

  BillingData({
    this.id,
    this.patientRegId,
    this.patientId,
    this.firstName,
    this.lastName,
    this.mobile,
    this.gender,
    this.age,
    this.services = const [],
    this.discountPercent,
    this.discountAmount,
    this.totalAmount,
    this.paidAmount,
    this.balanceAmount,
    this.paymentSource,
    this.createdAt,
    this.updatedAt,
  });

  factory BillingData.fromJson(Map<String, dynamic> json) {
    return BillingData(
      id: json['id']?.toString(),
      patientRegId: json['patient_reg_id']?.toString(),
      patientId: json['patient_id']?.toString(),
      firstName: json['firstName']?.toString(),
      lastName: json['lastName']?.toString(),
      mobile: json['mobile']?.toString(),
      gender: json['gender']?.toString(),
      age: json['age'] is int ? json['age'] : int.tryParse(json['age']?.toString() ?? ''),
      services: (json['services'] as List<dynamic>?)
              ?.map((item) => BillingService.fromJson(item))
              .toList() ??
          [],
      discountPercent: json['discount_percent']?.toString(),
      discountAmount: json['discount_amount']?.toString(),
      totalAmount: json['total_amount']?.toString(),
      paidAmount: json['paid_amount']?.toString(),
      balanceAmount: json['balance_amount']?.toString(),
      paymentSource: json['payment_source']?.toString(),
      createdAt: json['createdAt']?.toString(),
      updatedAt: json['updatedAt']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient_reg_id': patientRegId,
      'patient_id': patientId,
      'firstName': firstName,
      'lastName': lastName,
      'mobile': mobile,
      'gender': gender,
      'age': age,
      'services': services.map((service) => service.toJson()).toList(),
      'discount_percent': discountPercent,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'balance_amount': balanceAmount,
      'payment_source': paymentSource,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  // Helper methods
  String get displayName => '${firstName ?? ''} ${lastName ?? ''}'.trim();
  String get displayMobile => mobile ?? 'N/A';
  String get displayPaymentSource => paymentSource ?? 'N/A';
  String get displayPatientRegId => patientRegId ?? 'N/A';
  
  double get totalAmountValue => double.tryParse(totalAmount ?? '0') ?? 0.0;
  double get paidAmountValue => double.tryParse(paidAmount ?? '0') ?? 0.0;
  double get balanceAmountValue => double.tryParse(balanceAmount ?? '0') ?? 0.0;
  double get discountPercentValue => double.tryParse(discountPercent ?? '0') ?? 0.0;
  double get discountAmountValue => double.tryParse(discountAmount ?? '0') ?? 0.0;
}

class BillingService {
  final String? serviceId;
  final String? serviceName;
  final int quantity;
  final double price;

  BillingService({
    this.serviceId,
    this.serviceName,
    this.quantity = 1,
    this.price = 0.0,
  });

  factory BillingService.fromJson(Map<String, dynamic> json) {
    return BillingService(
      serviceId: json['service_id']?.toString(),
      serviceName: json['servicename']?.toString() ?? json['service_name']?.toString(),
      quantity: json['quantity'] is int ? json['quantity'] : int.tryParse(json['quantity']?.toString() ?? '1') ?? 1,
      price: json['price'] is double 
          ? json['price'] 
          : double.tryParse(json['price']?.toString() ?? '0') ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'service_id': serviceId,
      'service_name': serviceName,
      'quantity': quantity,
      'price': price,
    };
  }

  // Helper methods
  double get totalPrice => price * quantity;
  String get displayServiceName => serviceName ?? 'Unknown Service';
}

class ServiceModel {
  final String? id;
  final String? serviceName;
  final double price;
  final String? description;
  final bool isActive;

  ServiceModel({
    this.id,
    this.serviceName,
    this.price = 0.0,
    this.description,
    this.isActive = true,
  });

  factory ServiceModel.fromJson(Map<String, dynamic> json) {
    return ServiceModel(
      id: json['id']?.toString(),
      serviceName: json['servicename']?.toString() ?? json['service_name']?.toString(),
      price: json['price'] is double 
          ? json['price'] 
          : double.tryParse(json['price']?.toString() ?? '0') ?? 0.0,
      description: json['description']?.toString(),
      isActive: json['is_active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'servicename': serviceName,
      'price': price,
      'description': description,
      'is_active': isActive,
    };
  }

  String get displayServiceName => serviceName ?? 'Unknown Service';
  String get displayPrice => '₹${price.toStringAsFixed(2)}';
}

class ServicesListModel {
  final bool status;
  final List<ServiceModel> data;
  final String? message;

  ServicesListModel({
    required this.status,
    this.data = const [],
    this.message,
  });

  factory ServicesListModel.fromJson(Map<String, dynamic> json) {
    return ServicesListModel(
      status: json['status'] ?? false,
      data: (json['data'] as List<dynamic>?)
              ?.map((item) => ServiceModel.fromJson(item))
              .toList() ??
          [],
      message: json['message']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'data': data.map((service) => service.toJson()).toList(),
      'message': message,
    };
  }
}

class BillingUpsertRequest {
  final String? id;
  final String firstName;
  final String lastName;
  final String mobile;
  final String patientRegId;
  final String gender;
  final int age;
  final List<BillingServiceRequest> services;
  final String? discountPercent;
  final String? discountAmount;
  final String totalAmount;
  final String paidAmount;
  final String balanceAmount;
  final String paymentSource;

  BillingUpsertRequest({
    this.id,
    required this.firstName,
    required this.lastName,
    required this.mobile,
    required this.patientRegId,
    required this.gender,
    required this.age,
    required this.services,
    this.discountPercent,
    this.discountAmount,
    required this.totalAmount,
    required this.paidAmount,
    required this.balanceAmount,
    required this.paymentSource,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'firstName': firstName,
      'lastName': lastName,
      'mobile': mobile,
      'patient_reg_id': patientRegId,
      'gender': gender,
      'age': age,
      'services': services.map((service) => service.toJson()).toList(),
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'balance_amount': balanceAmount,
      'payment_source': paymentSource,
    };

    if (id != null) data['id'] = id;
    if (discountPercent != null && discountPercent!.isNotEmpty) {
      data['discount_percent'] = discountPercent;
    }
    if (discountAmount != null && discountAmount!.isNotEmpty) {
      data['discount_amount'] = discountAmount;
    }

    return data;
  }
}

class BillingServiceRequest {
  final String serviceName;
  final String serviceId;
  final int quantity;
  final double price;

  BillingServiceRequest({
    required this.serviceName,
    required this.serviceId,
    required this.quantity,
    required this.price,
  });

  Map<String, dynamic> toJson() {
    return {
      'service_name': serviceName,
      'service_id': serviceId,
      'quantity': quantity,
      'price': price,
    };
  }
}

class BillingUpsertResponse {
  final bool status;
  final String? message;
  final BillingData? data;

  BillingUpsertResponse({
    required this.status,
    this.message,
    this.data,
  });

  factory BillingUpsertResponse.fromJson(Map<String, dynamic> json) {
    return BillingUpsertResponse(
      status: json['status'] ?? false,
      message: json['message']?.toString(),
      data: json['data'] != null ? BillingData.fromJson(json['data']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }
}