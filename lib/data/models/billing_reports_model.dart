class BillingReportsModel {
  final List<BillingReportData> data;
  final PaginationData pagination;

  BillingReportsModel({
    required this.data,
    required this.pagination,
  });

  factory BillingReportsModel.fromJson(Map<String, dynamic> json) {
    return BillingReportsModel(
      data: (json['data'] as List<dynamic>?)
              ?.map((item) => BillingReportData.fromJson(item))
              .toList() ??
          [],
      pagination: PaginationData.fromJson(json['pagination'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((item) => item.toJson()).toList(),
      'pagination': pagination.toJson(),
    };
  }
}

class BillingReportData {
  final String? patientRegId;
  final String? name;
  final String? mobile;
  final String? paymentSource;
  final String? id;

  BillingReportData({
    this.patientRegId,
    this.name,
    this.mobile,
    this.paymentSource,
    this.id,
  });

  factory BillingReportData.fromJson(Map<String, dynamic> json) {
    return BillingReportData(
      patientRegId: json['patient_reg_id']?.toString(),
      name: json['name']?.toString(),
      mobile: json['mobile']?.toString(),
      paymentSource: json['payment_source']?.toString(),
      id: json['id']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'patient_reg_id': patientRegId,
      'name': name,
      'mobile': mobile,
      'payment_source': paymentSource,
      'id': id,
    };
  }

  // Helper method to get display name with fallback
  String get displayName => name?.isNotEmpty == true ? name! : 'N/A';
  
  // Helper method to get display mobile with fallback
  String get displayMobile => mobile?.isNotEmpty == true ? mobile! : 'N/A';
  
  // Helper method to get display payment source with fallback
  String get displayPaymentSource => paymentSource?.isNotEmpty == true ? paymentSource! : 'N/A';
  
  // Helper method to get display patient reg ID with fallback
  String get displayPatientRegId => patientRegId?.isNotEmpty == true ? patientRegId! : 'N/A';
}

class PaginationData {
  final int total;
  final int page;
  final int limit;
  final int totalPages;

  PaginationData({
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
  });

  factory PaginationData.fromJson(Map<String, dynamic> json) {
    return PaginationData(
      total: json['total'] ?? 0,
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 10,
      totalPages: json['totalPages'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'page': page,
      'limit': limit,
      'totalPages': totalPages,
    };
  }

  // Helper methods for pagination
  bool get hasNextPage => page < totalPages;
  bool get hasPreviousPage => page > 1;
  int get nextPage => hasNextPage ? page + 1 : page;
  int get previousPage => hasPreviousPage ? page - 1 : page;
}
