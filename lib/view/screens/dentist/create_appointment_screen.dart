import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/widgets/custom_elevated_button.dart';
import 'package:platix/view/widgets/label_text_field.dart'; // Using LabelTextField for consistency
import 'package:intl/intl.dart'; // For date formatting
import 'package:platix/view/screens/dentist/patient_registration_screen.dart'; // Import the new patient registration screen

class CreateAppointmentScreen extends StatefulWidget {
  const CreateAppointmentScreen({super.key});

  @override
  State<CreateAppointmentScreen> createState() => _CreateAppointmentScreenState();
}

class _CreateAppointmentScreenState extends State<CreateAppointmentScreen> {
  final TextEditingController patientNameController = TextEditingController();
  final TextEditingController serviceNameController = TextEditingController();
  final TextEditingController doctorNameController = TextEditingController();

  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;

  @override
  void dispose() {
    patientNameController.dispose();
    serviceNameController.dispose();
    doctorNameController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime ?? TimeOfDay.now(),
    );
    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Appointment'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LabelTextField(
                label: 'Patient Name',
                hint: 'Search Patient Name',
                controller: patientNameController,
                suffix: Icon(Icons.search), // Make it a search field
              ),
              const SizedBox(height: 10),
              GestureDetector(
                onTap: () {
                  Get.toNamed(AppRoutes.patientRegistrationScreen); // Navigate to new patient registration
                },
                child: Row(
                  children: [
                    const Icon(Icons.add_circle, color: AppColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'Add New Patient',
                      style: CustomTextStyles.b4_1.copyWith(color: AppColors.primary),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              GestureDetector(
                onTap: () => _selectDate(context),
                child: AbsorbPointer(
                  child: LabelTextField(
                    label: 'Date',
                    hint: _selectedDate == null
                        ? 'Select Date'
                        : DateFormat('dd/MM/yyyy').format(_selectedDate!),
                    controller: TextEditingController(text: _selectedDate == null ? '' : DateFormat('dd/MM/yyyy').format(_selectedDate!)),
                    suffix: Icon(Icons.calendar_today),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              GestureDetector(
                onTap: () => _selectTime(context),
                child: AbsorbPointer(
                  child: LabelTextField(
                    label: 'Time',
                    hint: _selectedTime == null
                        ? 'Select Time'
                        : _selectedTime!.format(context),
                    controller: TextEditingController(text: _selectedTime == null ? '' : _selectedTime!.format(context)),
                    suffix: Icon(Icons.access_time),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              LabelTextField(
                label: 'Service Name',
                hint: 'Search Service',
                controller: serviceNameController,
                suffix: Icon(Icons.search),
              ),
              const SizedBox(height: 20),
              LabelTextField(
                label: 'Doctor Name',
                hint: 'Select Doctor',
                controller: doctorNameController,
                suffix: Icon(Icons.arrow_drop_down),
              ),
              const SizedBox(height: 30),
              CustomElevatedButton(
                onPressed: () {
                  // Handle save logic
                  Get.snackbar('Success', 'Appointment Created Successfully!',
                  backgroundColor: AppColors.primary4,
                  colorText: Colors.white,
                  duration: const Duration(seconds: 3),
                  snackPosition: SnackPosition.TOP,
                );
                },
                text: 'Save',
              ),
            ],
          ),
        ),
      ),
    );
  }
}
