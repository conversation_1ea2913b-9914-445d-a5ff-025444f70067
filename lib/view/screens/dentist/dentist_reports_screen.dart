
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:excel/excel.dart' as exceldata;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:platix/controllers/dentist_controllers/dentist_reports_controller.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/view/screens/dentist/dentist_order_report_details.dart' as report;
import 'package:platix/view/screens/dentist/dentist_payment_report_details.dart';
import 'package:share_plus/share_plus.dart';
import '../../../api/data_store.dart';
import '../../../data/models/dentist/dentist_report_model.dart';
import '../../../data/models/dentist/dentist_report_order_info.dart';
import '../../../theme/app_decoration.dart';
import '../../../theme/custom_text_style.dart';
import '../../../utils/app_utils.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/constants/icon_constants.dart';
import '../../../utils/constants/sizes.dart';
import '../../../utils/web_responsive_utils.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/custom_elevated_button.dart';
import '../../widgets/custom_image_view.dart';
import '../../widgets/custom_search_dropdown.dart';
import 'dentist_homescreen.dart';
import 'dentist_orders_screen.dart';
import 'dentist_profilescreen.dart';

class DentistReportsScreen extends StatefulWidget {
  const DentistReportsScreen({super.key});

  @override
  State<DentistReportsScreen> createState() => _DentistReportsScreenState();
}

class _DentistReportsScreenState extends State<DentistReportsScreen> with TickerProviderStateMixin{
  final fromOrg = getData.read("userRecord")?['organization']?['name']?? '';
  DateTime? orderDate;
  DateTime? requiredDate;
  final DentistReportController dentistReportController = Get.find();
  final PermissionService _permissionService = PermissionService();
  late TabController _tabController;
  Future<List<String>> fetchItems(String query) async {
    List<String> items = [
      "Order1",
      "Order2",
      "Order2",
      "Order2",
    ];

    await Future.delayed(const Duration(milliseconds: 500));

    return items
        .where((item) => item.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  List<dynamic> filteredDataList = []; // Holds search results
  bool isSearching = false; // To track if searching is active
  int _selectedTabIndex = 0;

  // @override
  // void initState() {
  //   // TODO: implement initState
  //   super.initState();
  //   _tabController = TabController(length: 2, vsync: this);
  //   dentistReportController.getReport();
  //   log("🔍 Debug: orderDate = $orderDate, requiredDate = $requiredDate");
  //   if (orderDate != null && requiredDate != null) {
  //     dentistReportController.searchDate(orderDate!, requiredDate!);
  //   }
  //   else{
  //     log("empty");
  //   }
  //   fetchReports();
  //
  // }


  @override
  void initState() {
    super.initState();
    // Calculate tab count based on permissions
    int tabCount = 0;
    if (_permissionService.hasAnyPermission('order_reports', ['is_view', 'is_list'])) tabCount++;
    if (_permissionService.hasAnyPermission('payment_reports', ['is_view', 'is_list'])) tabCount++;

    _tabController = TabController(length: tabCount > 0 ? tabCount : 1, vsync: this);
    dentistReportController.fetchReport();
    log("🔍 Debug: orderDate = $orderDate, requiredDate = $requiredDate");

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (orderDate != null && requiredDate != null) {
        dentistReportController.searchDate(orderDate!, requiredDate!);
      } else {
      }
      fetchReports();
    });

  }

  Future<void> fetchReports() async {
    if (dentistReportController.reportsModel != null) {
      filteredDataList = List.from(dentistReportController.reportsModel!.data);
      print(jsonEncode(filteredDataList));
      log("list");
    } else {
      filteredDataList = []; // Ensure it's an empty list if there's no data
    }

  }


  @override
  void dispose() {
    super.dispose();
    _tabController.dispose();
  }

  Future<void> performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        isSearching = false;
      });

      await dentistReportController.fetchReport(); // ✅ Re-fetch all data from API

      setState(() {
        filteredDataList = List.from(dentistReportController.reportsModel?.data ?? []);
      });
      return;
    }

    setState(() {
      isSearching = true;
    });

    await dentistReportController.searchReport(query);

    setState(() {
      log("🛠️ Filtered Order Reports: ${jsonEncode(dentistReportController.filteredOrderReports)}");
      filteredDataList = List.from(dentistReportController.filteredOrderReports);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar:
      kIsWeb
          ?WebResponsiveUtils.dentistWebAppBar(2,context)
          :const CustomAppBar(
        leadingWidth: 0,
        title: 'Reports',
        centerTitle: true,
        backgroundColor: AppColors.primary,
        textColor: Colors.white,
      ),
      drawer: (MediaQuery.of(context).size.width <= 600 && kIsWeb)
          ? Drawer(
        child: ListView(
          children: [
            DrawerHeader(
              decoration: const BoxDecoration(color: AppColors.primary),
              child: CustomImageView(
                fit: BoxFit.none,
                imagePath: AppIcons.appLogo,
                color: AppColors.white,
              ),
            ),
            ListTile(
              leading: CustomImageView(
                imagePath: AppIcons.home,
              ),
              title: const Text('Home'),
              onTap: () {
                Get.offAll(() => const DentistHomeScreen());
              },
            ),
            ListTile(
              //leading: Icon(AppIcons.orders),
              leading: CustomImageView(
                imagePath: AppIcons.orders,
              ),
              title: const Text('Order'),
              onTap: () {
                Get.offAll(() =>  DentistOrdersScreen());
              },
            ),
            ListTile(
              //leading: Icon(AppIcons.orders),
              leading: CustomImageView(
                imagePath: AppIcons.reports,
              ),
              title: const Text('Reports'),
              onTap: () {
                Get.offAll(() => const DentistReportsScreen());
              },
            ),
            ListTile(
              //leading: Icon(AppIcons.orders),
              leading: CustomImageView(
                imagePath: AppIcons.profile,
              ),
              title: const Text('Profile'),
              onTap: () {
                Get.offAll(() => const DentistProfilescreen());
              },
            ),

          ],
        ),
      )
          : null,
      body: SafeArea(
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: AppSizes.defaultSpace,
              ),
              Center(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                  height: 48,
                  width: MediaQuery.of(context).size.width >600 ? Get.size.width* 0.5 : double.infinity,
                  decoration: BoxDecoration(
                    color: AppColors.background1,
                    borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm), // Optional
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withOpacity(0.1),
                        offset: const Offset(0, 1),
                        blurRadius: 2,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: CustomSearchField<Map<String, dynamic>>(
                    fetchItems: (query) async => [],
                    hintText: "Search orders",
                    itemAsString: (item) => item["name"] ?? "",
                    onSelected: (item) {

                    },
                    onChanged: (query) {
                      log("🔍 Search Query: $query");
                      performSearch(query); // ✅ Always call it
                    },


                    // defaultItems: const ["Order1", "Order2", "Order3"], // Optional
                  ),
                ),
              ),
              const SizedBox(height: AppSizes.defaultSpace,),

              Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                child: Column(
                  children: [
                    SizedBox(
                      height: 35,
                      child: TabBar(
                        controller: _tabController,
                        unselectedLabelColor: AppColors.black,
                        indicatorColor: AppColors.primary,
                        labelColor: AppColors.black,
                        labelStyle: CustomTextStyles.b4_1,
                        onTap: (index)async{
                          await dentistReportController.fetchReport();
                          setState(() {
                            _selectedTabIndex = index;
                            orderDate=null;
                            requiredDate=null;
                          });

                        },
                        tabs: [
                          if (_permissionService.hasAnyPermission('order_reports', ['is_view', 'is_list']))
                            const Padding(
                              padding: EdgeInsets.symmetric(horizontal: 10.0),
                              child: Tab(
                                text: "Orders Reports",
                              ),
                            ),
                          if (_permissionService.hasAnyPermission('payment_reports', ['is_view', 'is_list']))
                            const Padding(
                              padding: EdgeInsets.symmetric(horizontal: 10.0) ,
                              child: Tab(
                                text: "Payment Reports",
                              ),
                            ),
                        ],
                      ),

                    ),
                    const SizedBox(height: AppSizes.defaultSpace,),


                      Row(
                        mainAxisAlignment: MediaQuery.of(context).size.width > 600 ? MainAxisAlignment.center : MainAxisAlignment.start,
                        children: [
                          _buildDateField(orderDate, (date) {
                            setState(() {
                              orderDate = date;
                              print("Selected From Date: $orderDate");
                              if (orderDate != null && requiredDate != null) {
                                dentistReportController.searchDate(orderDate!, requiredDate!);
                              }
                            });
                          }, "From"),

                          const SizedBox(width: AppSizes.spaceSmall),

                          _buildDateField(requiredDate, (date) {
                            setState(() {
                              requiredDate = date;
                              print("Selected To Date: $requiredDate");
                              if (orderDate != null && requiredDate != null) {
                                dentistReportController.searchDate(orderDate!, requiredDate!);
                              }
                            });
                          }, "To"),

                          MediaQuery.of(context).size.width > 600 ? const SizedBox(width: AppSizes.defaultSpace,) :  Spacer(),
                          GetBuilder<DentistReportController>(
                            builder: (controller) {
                              return
                              //   CustomElevatedButton(
                              //   // onPressed: () async {
                              //   //   try {
                              //   //     var data = dentistReportController.reportsModel?.data;
                              //   //     if (data != null) {
                              //   //       print("Orders Detail Model: $data");
                              //   //       await generateOwnerReportExcel(data);
                              //   //       ScaffoldMessenger.of(context).showSnackBar(
                              //   //         const SnackBar(
                              //   //           content: Text(' Reports downloaded successfully!'),
                              //   //           backgroundColor: Colors.green,
                              //   //           duration: Duration(seconds: 2),
                              //   //         ),
                              //   //       );
                              //   //       await Future.delayed(const Duration(seconds: 2));
                              //   //     } else {
                              //   //       print("Error: OrdersDetailModel or data is null");
                              //   //     }
                              //   //   } catch (e, stackTrace) {
                              //   //     print("Error generating report: $e");
                              //   //     print(stackTrace);
                              //   //   }
                              //   // },
                              //
                              //   onPressed: () async {
                              //     try {
                              //       var data = dentistReportController.reportsModel?.data;
                              //       if (data != null && data.isNotEmpty) {
                              //         print("Orders Detail Model: $data");
                              //         await generateOwnerReportExcel(data);
                              //         ScaffoldMessenger.of(context).showSnackBar(
                              //           const SnackBar(
                              //             content: Text('Reports downloaded successfully!'),
                              //             backgroundColor: Colors.green,
                              //             duration: Duration(seconds: 2),
                              //           ),
                              //         );
                              //         await Future.delayed(const Duration(seconds: 2));
                              //       } else {
                              //         ScaffoldMessenger.of(context).showSnackBar(
                              //           const SnackBar(
                              //             content: Text('No orders found.'),
                              //             backgroundColor: Colors.orange,
                              //             duration: Duration(seconds: 2),
                              //           ),
                              //         );
                              //       }
                              //     } catch (e, stackTrace) {
                              //       print("Error generating report: $e");
                              //       print(stackTrace);
                              //     }
                              //   },
                              //
                              //
                              //   buttonStyle: ElevatedButton.styleFrom(
                              //     shape: RoundedRectangleBorder(
                              //       borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                              //     ),
                              //     shadowColor: AppColors.black.withOpacity(0.25),
                              //   ),
                              //   leftIcon: Padding(
                              //     padding: const EdgeInsets.only(bottom: 2.0),
                              //     child: CustomImageView(imagePath: AppIcons.download),
                              //   ),
                              //   text: 'Download',
                              //   buttonTextStyle: CustomTextStyles.b6_1.copyWith(color: AppColors.white),
                              //   height: 32,
                              //   width: MediaQuery.of(context).size.width > 600 ? 119 : Get.size.width * 0.28,
                              // );

                                CustomElevatedButton(
                                  onPressed: () async {
                                    try {
                                      var data = dentistReportController.reportsModel?.data;
                                      if (data != null && data.isNotEmpty) {
                                        print("Orders Detail Model: $data");
                                        await generateOwnerReportExcel(data);

                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(
                                            content: Text(Platform.isAndroid
                                                ? 'Report downloaded successfully!'
                                                : 'Report ready for sharing!'),
                                            backgroundColor: Colors.green,
                                            duration: const Duration(seconds: 2),
                                          ),
                                        );
                                      } else {
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          const SnackBar(
                                            content: Text('No orders found.'),
                                            backgroundColor: Colors.orange,
                                            duration: Duration(seconds: 2),
                                          ),
                                        );
                                      }
                                    } catch (e, stackTrace) {
                                      print("Error generating report: $e");
                                      print(stackTrace);
                                    }
                                  },
                                  buttonStyle: ElevatedButton.styleFrom(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                                    ),
                                    shadowColor: AppColors.black.withOpacity(0.25),
                                  ),
                                  leftIcon: Padding(
                                    padding: const EdgeInsets.only(bottom: 2.0),
                                    child: CustomImageView(
                                      imagePath: Platform.isAndroid ? AppIcons.download : AppIcons.download,
                                    ),
                                  ),
                                  text: Platform.isAndroid ? 'Download' : 'Share',
                                  buttonTextStyle: CustomTextStyles.b6_1.copyWith(color: AppColors.white),
                                  height: 32,
                                  width: MediaQuery.of(context).size.width > 600 ? 119 : Get.size.width * 0.28,
                                );

                            },
                          )


                        ],




                      ),

                    const SizedBox(height: AppSizes.spaceSmall,),

                  ],
                ),
              ),


              // text: 'Download ',
              // buttonTextStyle: CustomTextStyles.b6_1.copyWith(color: AppColors.white),
              // height: 32,
              // width: MediaQuery.of(context).size.width > 600 ? 119 : Get.size.width*0.28,
              Expanded(
                child:SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: SizedBox(
                    height: MediaQuery.of(context).size.height * 0.7,
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        if (_permissionService.hasAnyPermission('order_reports', ['is_view', 'is_list']))
                          orderReports(),
                        if (_permissionService.hasAnyPermission('payment_reports', ['is_view', 'is_list']))
                          paymentReports(),
                      ],
                    ),
                  ),
                ),

              ),
            ],

          ),
        ),

      ),

    );
  }

  Widget orderReports() {
    return GetBuilder<DentistReportController>(builder: (controller) {
      final List<Datum> orderReports1 = controller.isSearching.value
          ? controller.filteredOrderReports
          : (controller.reportsModel?.data ?? []);

      if (controller.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (orderReports1.isEmpty) {
        return const Center(child: Text('No Order Reports Available'));
      }

      return RefreshIndicator(
        onRefresh: () async {
          await controller.fetchReport();
        },
        child: GridView.builder(
          padding: const EdgeInsets.only(
            top: 16,
            left: AppSizes.md,
            right: AppSizes.md,
            bottom: 130,
          ),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: WebResponsiveUtils.responsiveGridItemCount(context),
            mainAxisExtent: 100,
            mainAxisSpacing: 10,
            crossAxisSpacing: 10,
          ),
          itemCount: orderReports1.length,
          itemBuilder: (context, index) {
            final order = orderReports1[index];

            return GestureDetector(
              onTap: () {
                Get.to(() => report.DentistOrderReportDetails(orderId: order.id));
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: AppSizes.md, vertical: AppSizes.sm),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.black.withOpacity(0.16),
                      blurRadius: 4,
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Order Id: ${order.orderId}',
                      style: CustomTextStyles.b5.copyWith(color: AppColors.darkerGrey),
                    ),
                    const SizedBox(height: 1),
                    Text(
                      'From: $fromOrg',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: CustomTextStyles.b5.copyWith(color: AppColors.darkerGrey),
                    ),
                    const SizedBox(height: 1),
                    Text(
                      'Order date: ${order.orderDate != null ? DateFormat('dd-MM-yyyy').format(order.orderDate) : 'N/A'}',
                      style: CustomTextStyles.b5.copyWith(color: AppColors.darkerGrey),
                    ),
                    const SizedBox(height: 1),
                    Row(
                      children: [
                        Text(
                          'Order Status : ',
                          style: CustomTextStyles.b5.copyWith(color: AppColors.darkerGrey),
                        ),
                        Text(
                          "${order.orderStatus[0].toUpperCase()}${order.orderStatus.substring(1)}",
                          style: CustomTextStyles.b5.copyWith(color: AppColors.darkGreen),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      );
    });
  }


  Widget paymentReports() {
    return GetBuilder<DentistReportController>(builder: (controller) {
      final List<Datum> orderReports1 = controller.reportsModel?.data ?? [];

      if (controller.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (orderReports1.isEmpty) {
        return const Center(child: Text('No Payment Reports Available'));
      }

      return RefreshIndicator(
        onRefresh: () async {
          await controller.fetchReport();
        },
        child: GridView.builder(
          padding: const EdgeInsets.only(
            top: 16,
            left: AppSizes.md,
            right: AppSizes.md,
            bottom: 130, // 👈 Ensures the last item isn't cut off
          ),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: WebResponsiveUtils.responsiveGridItemCount(context),
            mainAxisExtent: 100,
            mainAxisSpacing: 10,
            crossAxisSpacing: 10,
          ),
          itemCount: orderReports1.length,
          itemBuilder: (context, index) {
            final order = orderReports1[index];
            return GestureDetector(
              onTap: () {
                Get.to(() => DentistPaymentReportDetails(orderId: order.id));
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: AppSizes.md, vertical: AppSizes.sm),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.black.withOpacity(0.16),
                      blurRadius: 4,
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Order Id: ${order.orderId}',
                      style: CustomTextStyles.b5.copyWith(color: AppColors.darkerGrey),
                    ),
                    const SizedBox(height: 1),
                    Text(
                      'From: $fromOrg',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: CustomTextStyles.b5.copyWith(color: AppColors.darkerGrey),
                    ),
                    const SizedBox(height: 1),
                    Text(
                      'Order date: ${order.orderDate != null ? DateFormat('dd-MM-yyyy').format(order.orderDate) : 'N/A'}',
                      style: CustomTextStyles.b5.copyWith(color: AppColors.darkerGrey),
                    ),
                    const SizedBox(height: 1),
                    Row(
                      children: [
                        Text(
                          'Order Status : ',
                          style: CustomTextStyles.b5.copyWith(color: AppColors.darkerGrey),
                        ),
                        Text(
                            "${order.orderStatus[0].toUpperCase()}${order.orderStatus.substring(1)}",

                          style: CustomTextStyles.b5.copyWith(color: AppColors.darkGreen),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      );
    });
  }


  Widget _buildDateField(DateTime? date, Function(DateTime) onDateSelected,String hintText) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            if (hintText == "To" && orderDate != null) {
              // Set From Date as the minimum selectable date in calendar
              _selectDate(context, onDateSelected, firstDate: orderDate);
            } else {
              _selectDate(context, onDateSelected);
            }
          },
          child: Container(
            height: 32,
            width: Get.size.width*0.3,
            // padding: const EdgeInsets.symmetric(horizontal: AppSizes.md, vertical: AppSizes.md),
            decoration: BoxDecoration(
              boxShadow: AppDecoration.shadow1_3,
              color: Colors.white,
              borderRadius: BorderRadiusStyle.radius8,
            ),
            child: Padding(
              padding:const EdgeInsets.symmetric(horizontal: AppSizes.sm2),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    date != null ? DateFormat('dd-MM-yyyy').format(date) : hintText,
                    style: date != null
                        ? CustomTextStyles.b6.copyWith(color: AppColors.black, fontWeight: FontWeight.w500)
                        : CustomTextStyles.b6_1.copyWith(color: AppColors.primary),
                  ),
                  CustomImageView(imagePath: AppIcons.calender,
                    color: AppColors.primary,
                    height: 16,
                    width: 16,),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Future<void> _selectDate(BuildContext context, Function(DateTime) onDateSelected) async {
  //   DateTime? pickedDate = await showDatePicker(
  //     context: context,
  //     initialDate: DateTime.now(),
  //     firstDate: DateTime(2000),
  //     lastDate: DateTime(2100),
  //   );
  //   if (pickedDate != null) {
  //     onDateSelected(pickedDate);
  //   }
  // }
  Future<void> _selectDate(
      BuildContext context,
      Function(DateTime) onDateSelected, {
        DateTime? firstDate,
        DateTime? lastDate,
      }) async {
    DateTime now = DateTime.now();
    DateTime initialDate = firstDate != null && firstDate.isAfter(now) ? firstDate : now;

    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate ?? DateTime(2000),
      lastDate: lastDate ?? DateTime(2100),
    );

    if (pickedDate != null) {
      onDateSelected(pickedDate);
    }
  }



//   Future<void> generateOwnerReportExcel(List<Datum> orders) async {
//     // int calculateBalance() {
//     //   String totalStr = dentistReportController.reportDetails?.totalAmount ?? '0';
//     //   String paidStr = dentistReportController.reportDetails?.paidAmount ?? '0';
//     //
//     //   // Convert safely, handling decimals by parsing as double first
//     //   double totalAmount = double.tryParse(totalStr) ?? 0.0;
//     //   double paidAmount = double.tryParse(paidStr) ?? 0.0;
//     //
//     //   return (totalAmount - paidAmount).round();
//     // }
//     // int calculateBalance({required Datum order}) {
//     //   String? totalStr = order.totalAmount;
//     //   String? paidStr = order.paidAmount;
//     //
//     //   log('Raw totalAmount: "$totalStr"');
//     //   log('Raw paidAmount: "$paidStr"');
//     //
//     //   if (totalStr == null || totalStr.trim().isEmpty || totalStr == "null") {
//     //     log('⚠️ totalAmount is null, empty, or "null", defaulting to 0');
//     //     totalStr = '0';
//     //   }
//     //   if (paidStr == null || paidStr.trim().isEmpty || paidStr == "null") {
//     //     log('⚠️ paidAmount is null, empty, or "null", defaulting to 0');
//     //     paidStr = '0';
//     //   }
//     //
//     //   double totalAmount = double.tryParse(totalStr) ?? 0.0;
//     //   double paidAmount = double.tryParse(paidStr) ?? 0.0;
//     //
//     //   log('✅ Parsed totalAmount: $totalAmount');
//     //   log('✅ Parsed paidAmount: $paidAmount');
//     //
//     //   double balance = totalAmount - paidAmount;
//     //   log('✅ Calculated balance: $balance');
//     //
//     //   return balance.round();
//     // }
//
//     final fromOrg = '${getData.read("userRecord")['organization']["name"]}';
//     // Create a new Excel workbook and sheet.
//     var excel = exceldata.Excel.createExcel();
//
// // Create your own sheet
//     excel.rename('Sheet1', 'Report'); // rename existing Sheet1 to Report
//
// // Now you can safely access 'Report' sheet
//     exceldata.Sheet sheet = excel['Report'];
//
//
//
//     // -------------------
//     // Setup Header Rows:
//     // -------------------
//     // Let's assume our final columns are:
//     // A: Order ID, B: Order Date, C: Order From, D: Order To,
//     // E: Patient ID, F: Patient Name,
//     // G-I: Services (merged header with sub-headers: Service Name, Price, Quantity),
//     // J: Order Status, K: Balance, L: Bill Amount, M: Paid Amount,
//     // N: Paid Date, O: Payment Source, P: Remark, Q: Payment Status
//
//     // First row: headers
//     sheet.cell(exceldata.CellIndex.indexByString("A1")).value = exceldata.TextCellValue("Order ID");
//
//     sheet.cell(exceldata.CellIndex.indexByString("B1")).value = exceldata.TextCellValue("Order Date");
//     sheet.cell(exceldata.CellIndex.indexByString("C1")).value = exceldata.TextCellValue("Order From");
//     sheet.cell(exceldata.CellIndex.indexByString("D1")).value = exceldata.TextCellValue("Order To");
//     sheet.cell(exceldata.CellIndex.indexByString("E1")).value = exceldata.TextCellValue("Patient ID");
//     sheet.cell(exceldata.CellIndex.indexByString("F1")).value = exceldata.TextCellValue("Patient Name");
//     // Merge G1 to I1 for the main Services header.
//     sheet.merge(exceldata.CellIndex.indexByString("G1"), exceldata.CellIndex.indexByString("I1"));
//     sheet.cell(exceldata.CellIndex.indexByString("G1")).value = exceldata.TextCellValue("Services");
//     sheet.cell(exceldata.CellIndex.indexByString("J1")).value = exceldata.TextCellValue("Order Status");
//     sheet.cell(exceldata.CellIndex.indexByString("K1")).value = exceldata.TextCellValue("Balance");
//     sheet.cell(exceldata.CellIndex.indexByString("L1")).value = exceldata.TextCellValue("Bill Amount");
//     sheet.cell(exceldata.CellIndex.indexByString("M1")).value = exceldata.TextCellValue("Paid Amount");
//     sheet.cell(exceldata.CellIndex.indexByString("N1")).value = exceldata.TextCellValue("Paid Date");
//     sheet.cell(exceldata.CellIndex.indexByString("O1")).value = exceldata.TextCellValue("Payment Source");
//     sheet.cell(exceldata.CellIndex.indexByString("P1")).value = exceldata.TextCellValue("Remark");
//     sheet.cell(exceldata.CellIndex.indexByString("Q1")).value = exceldata.TextCellValue("Payment Status");
//
//     // Second row: Sub-column headers for Services in columns G, H, and I.
//     sheet.cell(exceldata.CellIndex.indexByString("G2")).value = exceldata.TextCellValue("Service Name");
//     sheet.cell(exceldata.CellIndex.indexByString("H2")).value = exceldata.TextCellValue("Price");
//     sheet.cell(exceldata.CellIndex.indexByString("I2")).value = exceldata.TextCellValue("Quantity");
//
//     // -------------------------
//     // Insert Order Data Rows:
//     // -------------------------
//     int row = 3;
//     for (var order in orders) {
//       // Order details in columns A-F.
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row - 1)).value = exceldata.TextCellValue(order.orderId.toString());
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row - 1)).value = exceldata.TextCellValue(order.orderDate!= null ? DateFormat('yyyy-MM-dd').format(order.orderDate) : 'N/A');
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row - 1)).value = exceldata.TextCellValue(fromOrg);
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row - 1)).value = exceldata.TextCellValue(order.toOrg.name);
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row - 1)).value = exceldata.TextCellValue(order.patientId.toString());
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row - 1)).value = exceldata.TextCellValue(order.patientName.toString());
//
//       // Services: If you want to show multiple services in the same row,
//       // you can join the values with a newline.
//       // Extracting service names
//       String serviceNames = order.orderServices != null
//           ? order.orderServices
//           .map((s) => s.orgservice.servicess.servicename ?? "Unknown")
//           .join("\n")
//           : "No Services";
//
//       String servicePrices = order.orderServices != null
//           ? order.orderServices.map((s) => s.price).join("\n")
//           : "No Prices";
//
//       String serviceQuantities = order.orderServices != null
//           ? order.orderServices.map((s) => s.quantity.toString()).join("\n")
//           : "No Quantities";
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row - 1)).value = exceldata.TextCellValue(serviceNames);
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row - 1)).value = exceldata.TextCellValue(servicePrices);
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: row - 1)).value = exceldata.TextCellValue(serviceQuantities);
//
//       // Remaining order info.
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 9, rowIndex: row - 1)).value = exceldata.TextCellValue(order.orderStatus.toString());
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 10, rowIndex: row - 1)).value = exceldata.TextCellValue("0");
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 11, rowIndex: row - 1)).value = exceldata.TextCellValue(order.totalAmount.toString());
//       sheet.cell(
//         exceldata.CellIndex.indexByColumnRow(columnIndex: 12, rowIndex: row - 1),
//       ).value = exceldata.TextCellValue(
//         (order.transactions != null && order.transactions.isNotEmpty)
//             ? order.transactions.first.amount.toString()
//             : 'N/A',
//       );
//
//       sheet.cell(
//         exceldata.CellIndex.indexByColumnRow(columnIndex: 13, rowIndex: row - 1),
//       ).value = exceldata.TextCellValue(
//         (order.transactions != null && order.transactions.isNotEmpty)
//             ? order.transactions.first.createdAt.toString()
//             : 'N/A',
//       );
//
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 14, rowIndex: row - 1)).value = exceldata.TextCellValue("Cash Free");
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 15, rowIndex: row - 1)).value = exceldata.TextCellValue(order.remarks.toString());
//       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 16, rowIndex: row - 1)).value = exceldata.TextCellValue(order.paymentStatus);
//       row++;
//     }
//
//     // -------------------------
//     // Save the Excel file
//     // -------------------------
//     final fileBytes = excel.encode();
//     if (fileBytes == null) return;
//
//     // Save to Downloads folder (Recommended)
//     final directory = Directory("/storage/emulated/0/Download");
//     if (!(await directory.exists())) {
//       directory.create(recursive: true); // Ensure directory exists
//     }
//     final file = File("${directory.path}/DentistReport_${DateTime.now().millisecondsSinceEpoch}.xlsx");
//
//
//     await file.writeAsBytes(fileBytes);
//     print("✅ File saved at: ${file.path}");
//
//     // Open the file after saving
//     Future.delayed(const Duration(seconds: 2), () {
//       OpenFile.open(file.path);
//     });
//
//
//     AppUtils.saveFileToDownloads(Uint8List.fromList(fileBytes), "DentistReport.xlsx");
//     //await _saveFileToCustomFolder(Uint8List.fromList(fileBytes), "OwnerReport.xlsx");
//
//   }


  Future<void> generateOwnerReportExcel(List<Datum> orders) async {
    final fromOrg = '${getData.read("userRecord")['organization']["name"]}';

    // Create a new Excel workbook and sheet
    var excel = exceldata.Excel.createExcel();
    excel.rename('Sheet1', 'Report');
    exceldata.Sheet sheet = excel['Report'];

    // -------------------
    // Setup Header Rows:
    // -------------------
    const headers = [
      "Order ID", "Order Date", "Order From", "Order To",
      "Patient ID", "Patient Name", "Services","Price","Quantity",

      "Order Status", "Balance", "Bill Amount",
      "Paid Amount", "Paid Date", "Payment Source",
      "Remark", "Payment Status"
    ];

    // Setting headers
    for (int i = 0; i < headers.length; i++) {
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = exceldata.TextCellValue(headers[i]);
    }
    for (int i = 0; i < headers.length; i++) {
      // Increase each column width

      sheet.getColumnWidths[i]=20.0;


    }

    // -------------------------
    // Insert Order Data Rows:
    // -------------------------
    int row = 1;
    for (var order in orders) {
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = exceldata.TextCellValue(order.orderId.toString());
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = exceldata.TextCellValue(order.orderDate != null ? DateFormat('yyyy-MM-dd').format(order.orderDate) : 'N/A');
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row)).value = exceldata.TextCellValue(fromOrg);
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row)).value = exceldata.TextCellValue(order.toOrg.name);
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row)).value = exceldata.TextCellValue(order.patientId.toString());
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row)).value = exceldata.TextCellValue(order.patientName.toString());

      // Services
      String serviceNames = order.orderServices != null
          ? order.orderServices.map((s) => s.orgservice.servicess.servicename).toList().join(",\r\n")
          : "No Services";

      String servicePrices = order.orderServices != null
          ? order.orderServices.map((s) => s.price).toList().join(",\r\n")
          : "No Prices";

      String serviceQuantities = order.orderServices != null
          ? order.orderServices.map((s) => s.quantity.toString()).toList().join(",\r\n")
          : "No Quantities";




      //  sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row - 1)).value = exceldata.TextCellValue(serviceNames);
        sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row )).value = exceldata.TextCellValue(servicePrices);
        sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: row )).value = exceldata.TextCellValue(serviceQuantities);

       sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row)).value = exceldata.TextCellValue("${serviceNames},");


      // Order Info
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 9, rowIndex: row)).value = exceldata.TextCellValue(order.orderStatus.toString());
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 10, rowIndex: row)).value = exceldata.TextCellValue("0");
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 11, rowIndex: row)).value = exceldata.TextCellValue(order.totalAmount.toString());
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 12, rowIndex: row)).value = exceldata.TextCellValue(order.transactions?.first.amount.toString() ?? 'N/A');
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 13, rowIndex: row)).value = exceldata.TextCellValue(order.transactions?.first.createdAt.toString() ?? 'N/A');
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 14, rowIndex: row)).value = exceldata.TextCellValue("Cash Free");
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 15, rowIndex: row)).value = exceldata.TextCellValue(order.remarks.toString());
      sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 16, rowIndex: row)).value = exceldata.TextCellValue(order.paymentStatus);
      row++;
    }

    // -------------------------
    // Save the Excel file
    // -------------------------
    final fileBytes = excel.encode();
    if (fileBytes == null) return;

    if (Platform.isAndroid) {
      final directory = Directory("/storage/emulated/0/Download");
      if (!(await directory.exists())) {
        await directory.create(recursive: true);
      }

      final file = File("${directory.path}/DentistReport_${DateTime.now().millisecondsSinceEpoch}.xlsx");
      await file.writeAsBytes(fileBytes);
      print("✅ File saved at: ${file.path}");

      // Open the file
      Future.delayed(const Duration(seconds: 2), () {
        OpenFile.open(file.path);
      });
      AppUtils.saveFileToDownloads(Uint8List.fromList(fileBytes), "DentistReport.xlsx");

    } else if (Platform.isIOS) {
      final directory = await getApplicationDocumentsDirectory();
      final file = File("${directory.path}/DentistReport_${DateTime.now().millisecondsSinceEpoch}.xlsx");
      await file.writeAsBytes(fileBytes);

      // Share the file
      await Share.shareXFiles([XFile(file.path)]);
    }

  }


// Future<void> generatePaymentReportExcel(List<DentistReportDetailModel> orders) async {
//   // Create a new Excel workbook and sheet.
//   var excel = Excel.createExcel();
//   Sheet sheet = excel['Report'];
//
//   // -------------------
//   // Setup Header Rows:
//   // -------------------
//   // Let's assume our final columns are:
//   // A: Order ID, B: Order Date, C: Order From, D: Order To,
//   // E: Patient ID, F: Patient Name,
//   // G-I: Services (merged header with sub-headers: Service Name, Price, Quantity),
//   // J: Order Status, K: Balance, L: Bill Amount, M: Paid Amount,
//   // N: Paid Date, O: Payment Source, P: Remark, Q: Payment Status
//
//   // First row: headers
//   sheet.cell(exceldata.CellIndex.indexByString("A1")).value = exceldata.TextCellValue("Order ID");
//
//   sheet.cell(exceldata.CellIndex.indexByString("B1")).value = exceldata.TextCellValue("Order Date");
//   sheet.cell(exceldata.CellIndex.indexByString("C1")).value = exceldata.TextCellValue("Order From");
//   sheet.cell(exceldata.CellIndex.indexByString("D1")).value = exceldata.TextCellValue("Order To");
//   sheet.cell(exceldata.CellIndex.indexByString("E1")).value = exceldata.TextCellValue("Patient ID");
//   sheet.cell(exceldata.CellIndex.indexByString("F1")).value = exceldata.TextCellValue("Patient Name");
//   // Merge G1 to I1 for the main Services header.
//   sheet.merge(exceldata.CellIndex.indexByString("G1"), exceldata.CellIndex.indexByString("I1"));
//   sheet.cell(exceldata.CellIndex.indexByString("G1")).value = exceldata.TextCellValue("Services");
//   sheet.cell(exceldata.CellIndex.indexByString("J1")).value = exceldata.TextCellValue("Order Status");
//   sheet.cell(exceldata.CellIndex.indexByString("K1")).value = exceldata.TextCellValue("Balance");
//   sheet.cell(exceldata.CellIndex.indexByString("L1")).value = exceldata.TextCellValue("Bill Amount");
//   sheet.cell(exceldata.CellIndex.indexByString("M1")).value = exceldata.TextCellValue("Paid Amount");
//   sheet.cell(exceldata.CellIndex.indexByString("N1")).value = exceldata.TextCellValue("Paid Date");
//   sheet.cell(exceldata.CellIndex.indexByString("O1")).value = exceldata.TextCellValue("Payment Source");
//   sheet.cell(exceldata.CellIndex.indexByString("P1")).value = exceldata.TextCellValue("Remark");
//   sheet.cell(exceldata.CellIndex.indexByString("Q1")).value = exceldata.TextCellValue("Payment Status");
//
//   // Second row: Sub-column headers for Services in columns G, H, and I.
//   sheet.cell(exceldata.CellIndex.indexByString("G2")).value = exceldata.TextCellValue("Service Name");
//   sheet.cell(exceldata.CellIndex.indexByString("H2")).value = exceldata.TextCellValue("Price");
//   sheet.cell(exceldata.CellIndex.indexByString("I2")).value = exceldata.TextCellValue("Quantity");
//
//   // -------------------------
//   // Insert Order Data Rows:
//   // -------------------------
//   int row = 3;
//   for (var order in orders) {
//     // Order details in columns A-F.
//     sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row - 1)).value = exceldata.TextCellValue(order.data.orderId.toString());
//     sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row - 1)).value = exceldata.TextCellValue(order.data.orderDate.toString());
//     // sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row - 1)).value = exceldata.TextCellValue(order.orderFrom.toString());
//     // sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row - 1)).value = exceldata.TextCellValue(order.orderTo.toString());
//     sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row - 1)).value = exceldata.TextCellValue(order.data.patientId.toString());
//     sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row - 1)).value = exceldata.TextCellValue(order.data.patientName.toString());
//
//     // Services: If you want to show multiple services in the same row,
//     // you can join the values with a newline.
//     // Extracting service names
//     String serviceNames = order.data.orderServices != null
//         ? order.data.orderServices!
//         .map((s) => s.orgservice?.servicess.servicename ?? "Unknown")
//         .join("\n")
//         : "No Services";
//
//     String servicePrices = order.data.orderServices != null
//         ? order.data.orderServices!.map((s) => s.price).join("\n")
//         : "No Prices";
//
//     String serviceQuantities = order.data.orderServices != null
//         ? order.data.orderServices!.map((s) => s.quantity.toString()).join("\n")
//         : "No Quantities";
//     sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row - 1)).value = exceldata.TextCellValue(serviceNames);
//     sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row - 1)).value = exceldata.TextCellValue(servicePrices);
//     sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: row - 1)).value = exceldata.TextCellValue(serviceQuantities);
//
//     // Remaining order info.
//     sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 9, rowIndex: row - 1)).value = exceldata.TextCellValue(order.data.orderStatus.toString());
//     // sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 10, rowIndex: row - 1)).value = IntCellValue(order.balance);
//     sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 11, rowIndex: row - 1)).value = exceldata.TextCellValue(order.data.totalAmount.toString());
//     sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 12, rowIndex: row - 1)).value = exceldata.TextCellValue(order.data.paidAmount.toString());
//     // sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 13, rowIndex: row - 1)).value = exceldata.TextCellValue(order.paidDate);
//     sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 14, rowIndex: row - 1)).value = exceldata.TextCellValue(order.data.paymentMethod.toString());
//     sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 15, rowIndex: row - 1)).value = exceldata.TextCellValue(order.data.remarks.toString());
//     sheet.cell(exceldata.CellIndex.indexByColumnRow(columnIndex: 16, rowIndex: row - 1)).value = exceldata.TextCellValue(order.data.paymentStatus);
//     row++;
//   }
//
//   // -------------------------
//   // Save the Excel file
//   // -------------------------
//   final fileBytes = excel.encode();
//   if (fileBytes == null) return;
//
//   AppUtils.saveFileToDownloads(Uint8List.fromList(fileBytes), "DentistReport.xlsx");
//   //await _saveFileToCustomFolder(Uint8List.fromList(fileBytes), "OwnerReport.xlsx");
//
// }








}
