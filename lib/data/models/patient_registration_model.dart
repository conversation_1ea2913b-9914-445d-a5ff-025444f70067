class PatientRegistrationModel {
  final bool status;
  final List<PatientData> data;
  final PaginationData pagination;

  PatientRegistrationModel({
    required this.status,
    required this.data,
    required this.pagination,
  });

  factory PatientRegistrationModel.fromJson(Map<String, dynamic> json) {
    return PatientRegistrationModel(
      status: json['status'] ?? true,
      data: (json['data'] as List<dynamic>?)
              ?.map((item) => PatientData.fromJson(item))
              .toList() ??
          [],
      pagination: PaginationData.fromJson(json['pagination'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'data': data.map((item) => item.toJson()).toList(),
      'pagination': pagination.toJson(),
    };
  }
}

class PatientData {
  final String? id;           // Database UUID primary key
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? mobile;
  final String? patientRegId;
  final String? name;         // Full name from backend (first_name + last_name)
  final String? gender;
  final String? age;
  final String? address;
  final String? country;      // Country code like 'IN' for India
  final String? state;        // State code
  final String? city;         // City name
  final String? dob;          // Date of birth
  final String? createdAt;
  final String? updatedAt;

  PatientData({
    this.id,
    this.firstName,
    this.lastName,
    this.email,
    this.mobile,
    this.patientRegId,
    this.name,
    this.gender,
    this.age,
    this.address,
    this.country,
    this.state,
    this.city,
    this.dob,
    this.createdAt,
    this.updatedAt,
  });

  factory PatientData.fromJson(Map<String, dynamic> json) {
    return PatientData(
      id: json['id']?.toString(),
      firstName: json['first_name']?.toString(),
      lastName: json['last_name']?.toString(),
      email: json['email']?.toString(),
      mobile: json['mobile']?.toString(),
      patientRegId: json['patient_reg_id']?.toString(),
      name: json['name']?.toString(), // Backend provides full name
      gender: json['gender']?.toString(),
      age: json['age']?.toString(),
      address: json['address']?.toString(),
      country: json['country']?.toString(),
      state: json['state']?.toString(),
      city: json['city']?.toString(),
      dob: json['dob']?.toString(),
      createdAt: json['createdAt']?.toString(),
      updatedAt: json['updatedAt']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'mobile': mobile,
      'patient_reg_id': patientRegId,
      'name': name,
      'gender': gender,
      'age': age,
      'address': address,
      'country': country,
      'state': state,
      'city': city,
      'dob': dob,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  // Helper method to get display name
  String get displayName => name ?? '${firstName ?? ''} ${lastName ?? ''}'.trim();
  
  // Helper method to format creation date
  String get formattedDate {
    if (createdAt == null) return '';
    try {
      final date = DateTime.parse(createdAt!);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return createdAt!;
    }
  }
}

class PaginationData {
  final int total;
  final int page;
  final int limit;
  final int totalPages;

  PaginationData({
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
  });

  factory PaginationData.fromJson(Map<String, dynamic> json) {
    return PaginationData(
      total: json['total'] ?? 0,
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 10,
      totalPages: json['totalPages'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'page': page,
      'limit': limit,
      'totalPages': totalPages,
    };
  }

  // Helper methods for pagination
  bool get hasNextPage => page < totalPages;
  bool get hasPreviousPage => page > 1;
  int get nextPage => hasNextPage ? page + 1 : page;
  int get previousPage => hasPreviousPage ? page - 1 : page;
}