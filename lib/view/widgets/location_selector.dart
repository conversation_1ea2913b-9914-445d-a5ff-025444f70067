import 'package:flutter/material.dart';
import 'package:platix/utils/app_export.dart';
import 'package:country_state_city/country_state_city.dart' as csc;

class LocationSelector extends StatefulWidget {
  final String? initialCountryId;
  final String? initialStateId;
  final String? initialCityName;
  final Function(String? countryId, String countryName) onCountryChanged;
  final Function(String? stateId, String stateName) onStateChanged;
  final Function(String cityName) onCityChanged;

  const LocationSelector({
    super.key,
    this.initialCountryId,
    this.initialStateId,
    this.initialCityName,
    required this.onCountryChanged,
    required this.onStateChanged,
    required this.onCityChanged,
  });

  @override
  State<LocationSelector> createState() => _LocationSelectorState();
}

class _LocationSelectorState extends State<LocationSelector> {
  List<csc.Country> countries = [];
  List<csc.State> states = [];
  List<csc.City> cities = [];

  csc.Country? selectedCountry;
  csc.State? selectedState;
  csc.City? selectedCity;

  bool isLoadingCountries = true;
  bool isLoadingStates = false;
  bool isLoadingCities = false;

  @override
  void initState() {
    super.initState();
    _loadCountries();
  }

  Future<void> _loadCountries() async {
    setState(() {
      isLoadingCountries = true;
    });

    try {
      final allCountries = await csc.getAllCountries();
      setState(() {
        countries = allCountries;
        isLoadingCountries = false;
      });

      // Set initial country if provided
      if (widget.initialCountryId != null) {
        final initialCountry = countries.firstWhere(
          (country) => country.isoCode == widget.initialCountryId,
          orElse: () => countries.first,
        );
        await _onCountryChanged(initialCountry);
      }
    } catch (e) {
      setState(() {
        isLoadingCountries = false;
      });
      debugPrint('Error loading countries: $e');
    }
  }

  Future<void> _onCountryChanged(csc.Country? country) async {
    if (country == null) return;

    setState(() {
      selectedCountry = country;
      selectedState = null;
      selectedCity = null;
      states = [];
      cities = [];
      isLoadingStates = true;
    });

    try {
      final countryStates = await csc.getStatesOfCountry(country.isoCode);
      setState(() {
        states = countryStates;
        isLoadingStates = false;
      });

      // Set initial state if provided
      if (widget.initialStateId != null) {
        final initialState = states.firstWhere(
          (state) => state.isoCode == widget.initialStateId,
          orElse: () => states.isNotEmpty ? states.first : csc.State(name: '', isoCode: '', countryCode: ''),
        );
        if (initialState.isoCode.isNotEmpty) {
          await _onStateChanged(initialState);
        }
      }
    } catch (e) {
      setState(() {
        isLoadingStates = false;
      });
      debugPrint('Error loading states: $e');
    }

    widget.onCountryChanged(country.isoCode, country.name);
  }

  Future<void> _onStateChanged(csc.State? state) async {
    if (state == null) return;

    setState(() {
      selectedState = state;
      selectedCity = null;
      cities = [];
      isLoadingCities = true;
    });

    try {
      final stateCities = await csc.getStateCities(state.countryCode, state.isoCode);
      setState(() {
        cities = stateCities;
        isLoadingCities = false;
      });

      // Set initial city if provided
      if (widget.initialCityName != null) {
        final initialCity = cities.firstWhere(
          (city) => city.name == widget.initialCityName,
          orElse: () => cities.isNotEmpty ? cities.first : csc.City(name: '', countryCode: '', stateCode: ''),
        );
        if (initialCity.name.isNotEmpty) {
          _onCityChanged(initialCity);
        }
      }
    } catch (e) {
      setState(() {
        isLoadingCities = false;
      });
      debugPrint('Error loading cities: $e');
    }

    widget.onStateChanged(state.isoCode, state.name);
  }

  void _onCityChanged(csc.City? city) {
    if (city == null) return;

    setState(() {
      selectedCity = city;
    });

    widget.onCityChanged(city.name);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Country Dropdown
        Text(
          'Country',
          style: CustomTextStyles.b4_1,
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: isLoadingCountries
              ? const Padding(
                  padding: EdgeInsets.all(12.0),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 8),
                      Text('Loading countries...'),
                    ],
                  ),
                )
              : DropdownButtonHideUnderline(
                  child: DropdownButton<csc.Country>(
                    value: selectedCountry,
                    hint: const Text('Select Country'),
                    isExpanded: true,
                    items: countries.map((country) {
                      return DropdownMenuItem<csc.Country>(
                        value: country,
                        child: Text(country.name),
                      );
                    }).toList(),
                    onChanged: _onCountryChanged,
                  ),
                ),
        ),

        const SizedBox(height: 15),

        // State Dropdown
        Text(
          'State',
          style: CustomTextStyles.b4_1,
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: isLoadingStates
              ? const Padding(
                  padding: EdgeInsets.all(12.0),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 8),
                      Text('Loading states...'),
                    ],
                  ),
                )
              : DropdownButtonHideUnderline(
                  child: DropdownButton<csc.State>(
                    value: selectedState,
                    hint: const Text('Select State'),
                    isExpanded: true,
                    items: states.map((state) {
                      return DropdownMenuItem<csc.State>(
                        value: state,
                        child: Text(state.name),
                      );
                    }).toList(),
                    onChanged: selectedCountry == null ? null : _onStateChanged,
                  ),
                ),
        ),

        const SizedBox(height: 15),

        // City Dropdown
        Text(
          'City',
          style: CustomTextStyles.b4_1,
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: isLoadingCities
              ? const Padding(
                  padding: EdgeInsets.all(12.0),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 8),
                      Text('Loading cities...'),
                    ],
                  ),
                )
              : selectedState == null
                  ? const Padding(
                      padding: EdgeInsets.all(12.0),
                      child: Text(
                        'Please select a state first',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  : cities.isEmpty
                      ? const Padding(
                          padding: EdgeInsets.all(12.0),
                          child: Text(
                            'No cities available for this state',
                            style: TextStyle(color: Colors.grey),
                          ),
                        )
                      : DropdownButtonHideUnderline(
                          child: DropdownButton<csc.City>(
                            value: selectedCity,
                            hint: Text('Select City (${cities.length} available)'),
                            isExpanded: true,
                            items: cities.map((city) {
                              return DropdownMenuItem<csc.City>(
                                value: city,
                                child: Text(city.name),
                              );
                            }).toList(),
                            onChanged: _onCityChanged,
                          ),
                        ),
        ),
      ],
    );
  }
}