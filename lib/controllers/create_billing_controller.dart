import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/api/api_service.dart';
import 'package:platix/data/models/billing_model.dart';
import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/utils/app_export.dart';

class CreateBillingController extends GetxController {
  final ApiService _apiService = ApiService();

  // Form controllers
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController mobileController = TextEditingController();
  final TextEditingController patientRegIdController = TextEditingController();
  final TextEditingController ageController = TextEditingController();
  final TextEditingController priceController = TextEditingController();
  final TextEditingController discountPercentController = TextEditingController();
  final TextEditingController discountAmountController = TextEditingController();
  final TextEditingController paidAmountController = TextEditingController();
  final TextEditingController searchController = TextEditingController();

  // Observable variables
  var isLoading = false.obs;
  var isDataLoading = false.obs;
  var hasError = false.obs;
  var errorMessage = ''.obs;
  
  // Form data
  var selectedGender = ''.obs;
  var selectedPaymentSource = ''.obs;
  var totalAmount = 0.0.obs;
  var balanceAmount = 0.0.obs;
  var calculatedPrice = 0.0.obs;
  
  // Patient search
  var searchQuery = ''.obs;
  var searchResults = <PatientData>[].obs;
  var showSearchResults = false.obs;
  var selectedPatient = Rxn<PatientData>();
  
  // Services
  var availableServices = <ServiceModel>[].obs;
  var selectedServices = <ServiceModel>[].obs;
  var serviceQuantities = <String, int>{}.obs;
  
  // Mode management
  var currentMode = BillingMode.create.obs;
  var billingId = ''.obs;
  
  // Form validation
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    super.onInit();
    _setupCalculationListeners();
    fetchServices();
  }

  @override
  void onClose() {
    _disposeControllers();
    super.onClose();
  }

  void _disposeControllers() {
    firstNameController.dispose();
    lastNameController.dispose();
    mobileController.dispose();
    patientRegIdController.dispose();
    ageController.dispose();
    priceController.dispose();
    discountPercentController.dispose();
    discountAmountController.dispose();
    paidAmountController.dispose();
    searchController.dispose();
  }

  void _setupCalculationListeners() {
    // Listen to changes in selected services and quantities
    ever(selectedServices, (_) => _calculateTotalAmount());
    ever(serviceQuantities, (_) => _calculateTotalAmount());
    
    // Listen to price changes
    priceController.addListener(_calculateTotalAmount);
    discountPercentController.addListener(() {
      onDiscountPercentChanged(discountPercentController.text);
    });
    discountAmountController.addListener(() {
      onDiscountAmountChanged(discountAmountController.text);
    });
    paidAmountController.addListener(_calculateBalanceAmount);
  }

  /// Initialize for editing existing billing
  void initializeForEdit(String id, BillingData billingData) {
    currentMode.value = BillingMode.edit;
    billingId.value = id;
    _populateFormWithBillingData(billingData);
  }

  /// Initialize for viewing existing billing
  void initializeForView(String id, BillingData billingData) {
    currentMode.value = BillingMode.view;
    billingId.value = id;
    _populateFormWithBillingData(billingData);
  }

  void _populateFormWithBillingData(BillingData billingData) {
    // Populate patient data
    firstNameController.text = billingData.firstName ?? '';
    lastNameController.text = billingData.lastName ?? '';
    mobileController.text = billingData.mobile ?? '';
    patientRegIdController.text = billingData.patientRegId ?? '';
    ageController.text = billingData.age?.toString() ?? '';
    selectedGender.value = billingData.gender ?? '';
    
    // Populate billing data
    discountPercentController.text = billingData.discountPercent ?? '';
    discountAmountController.text = billingData.discountAmount ?? '';
    paidAmountController.text = billingData.paidAmount ?? '';
    selectedPaymentSource.value = billingData.paymentSource ?? '';
    totalAmount.value = billingData.totalAmountValue;
    balanceAmount.value = billingData.balanceAmountValue;
    
    // Populate selected services
    if (billingData.services.isNotEmpty) {
      for (var billingService in billingData.services) {
        // Find the service in available services
        var service = availableServices.firstWhereOrNull(
          (s) => s.id == billingService.serviceId,
        );
        
        if (service != null) {
          selectedServices.add(service);
          serviceQuantities[service.id!] = billingService.quantity;
        }
      }
      
      // Calculate total price from services
      double totalPrice = billingData.services.fold(0.0, (sum, service) => 
        sum + (service.price * service.quantity));
      priceController.text = totalPrice.toString();
    }
    
    // Set selected patient
    selectedPatient.value = PatientData(
      id: billingData.patientId,
      firstName: billingData.firstName,
      lastName: billingData.lastName,
      mobile: billingData.mobile,
      patientRegId: billingData.patientRegId,
      gender: billingData.gender,
      age: billingData.age?.toString(),
    );
  }

  /// Fetch available services
  Future<void> fetchServices() async {
    try {
      isLoading.value = true;
      hasError.value = false;
      
      final response = await _apiService.getAllServices();
      if (response.status) {
        availableServices.value = response.data;
      } else {
        throw Exception(response.message ?? 'Failed to fetch services');
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      _showErrorSnackbar('Error fetching services: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  /// Search patients
  Future<void> searchPatients(String query) async {
    if (query.length < 2) {
      searchResults.clear();
      showSearchResults.value = false;
      return;
    }

    try {
      searchQuery.value = query;
      final response = await _apiService.searchPatientsForBilling(search: query);
      
      if (response.status) {
        searchResults.value = response.data;
        showSearchResults.value = true;
      } else {
        searchResults.clear();
        showSearchResults.value = false;
      }
    } catch (e) {
      print('Error searching patients: $e');
      searchResults.clear();
      showSearchResults.value = false;
    }
  }

  /// Select patient from search results
  void selectPatient(PatientData patient) {
    selectedPatient.value = patient;
    searchController.clear();
    showSearchResults.value = false;
    
    // Fill patient details
    firstNameController.text = patient.firstName ?? '';
    lastNameController.text = patient.lastName ?? '';
    mobileController.text = patient.mobile ?? '';
    patientRegIdController.text = patient.patientRegId ?? '';
    ageController.text = patient.age ?? '';
    selectedGender.value = patient.gender ?? '';
  }

  /// Add service to selected services
  void addService(ServiceModel service) {
    if (!selectedServices.contains(service)) {
      selectedServices.add(service);
      serviceQuantities[service.id!] = 1;
    }
  }

  /// Remove service from selected services
  void removeService(ServiceModel service) {
    selectedServices.remove(service);
    serviceQuantities.remove(service.id!);
  }

  /// Update service quantity
  void updateServiceQuantity(String serviceId, int quantity) {
    if (quantity < 1) return;
    serviceQuantities[serviceId] = quantity;
    _calculateTotalAmount();
  }

  /// Calculate total amount based on selected services
  void _calculateTotalAmount() {
    double total = 0.0;
    
    // If price is manually entered, use that value
    if (priceController.text.isNotEmpty) {
      total = double.tryParse(priceController.text) ?? 0.0;
    } else {
      // Calculate based on selected services
      for (var service in selectedServices) {
        int quantity = serviceQuantities[service.id!] ?? 1;
        total += service.price * quantity;
      }
      
      // Update price controller with calculated value
      priceController.text = total.toString();
    }
    
    calculatedPrice.value = total;
    
    // Apply discounts
    if (discountPercentController.text.isNotEmpty && discountAmountController.text.isEmpty) {
      double discountPercent = double.tryParse(discountPercentController.text) ?? 0.0;
      double discount = (total * discountPercent) / 100;
      total = total - discount;
    } else if (discountAmountController.text.isNotEmpty && discountPercentController.text.isEmpty) {
      double discountAmount = double.tryParse(discountAmountController.text) ?? 0.0;
      total = total - discountAmount;
    }
    
    totalAmount.value = total;
    _calculateBalanceAmount();
  }

  /// Calculate balance amount
  void _calculateBalanceAmount() {
    double paid = double.tryParse(paidAmountController.text) ?? 0.0;
    balanceAmount.value = totalAmount.value - paid;
  }

  /// Validate form
  bool _validateForm() {
    if (firstNameController.text.isEmpty ||
        lastNameController.text.isEmpty ||
        mobileController.text.isEmpty ||
        patientRegIdController.text.isEmpty ||
        ageController.text.isEmpty ||
        selectedGender.value.isEmpty ||
        selectedServices.isEmpty ||
        selectedPaymentSource.value.isEmpty) {
      _showErrorSnackbar('Please fill all required fields');
      return false;
    }

    if (selectedServices.isEmpty) {
      _showErrorSnackbar('Please select at least one service');
      return false;
    }

    return true;
  }

  /// Submit billing form
  Future<void> submitBilling() async {
    if (!_validateForm()) return;

    try {
      isLoading.value = true;
      hasError.value = false;

      // Prepare services data
      List<BillingServiceRequest> servicesData = selectedServices.map((service) {
        int quantity = serviceQuantities[service.id!] ?? 1;
        return BillingServiceRequest(
          serviceName: service.serviceName!,
          serviceId: service.id!,
          quantity: quantity,
          price: service.price,
        );
      }).toList();

      // Create request
      final request = BillingUpsertRequest(
        id: currentMode.value == BillingMode.edit ? billingId.value : null,
        firstName: firstNameController.text,
        lastName: lastNameController.text,
        mobile: mobileController.text,
        patientRegId: patientRegIdController.text,
        gender: selectedGender.value,
        age: int.parse(ageController.text),
        services: servicesData,
        discountPercent: discountPercentController.text.isNotEmpty ? discountPercentController.text : null,
        discountAmount: discountAmountController.text.isNotEmpty ? discountAmountController.text : null,
        totalAmount: totalAmount.value.toStringAsFixed(2),
        paidAmount: paidAmountController.text,
        balanceAmount: balanceAmount.value.toStringAsFixed(2),
        paymentSource: selectedPaymentSource.value,
      );

      final response = await _apiService.upsertBilling(request);

      if (response.status) {
        _showSuccessSnackbar(
          currentMode.value == BillingMode.edit 
            ? 'Billing updated successfully' 
            : 'Billing created successfully'
        );
        
        // Navigate back after a short delay
        await Future.delayed(const Duration(milliseconds: 500));
        Get.back(result: true);
      } else {
        _showErrorSnackbar(response.message ?? 'Failed to save billing');
      }
    } catch (e) {
      _showErrorSnackbar('Error: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  /// Clear discount percent when discount amount is entered
  void onDiscountAmountChanged(String value) {
    if (value.isNotEmpty) {
      discountPercentController.clear();
    }
    _calculateTotalAmount();
  }

  /// Clear discount amount when discount percent is entered
  void onDiscountPercentChanged(String value) {
    if (value.isNotEmpty) {
      discountAmountController.clear();
    }
    _calculateTotalAmount();
  }

  void _showSuccessSnackbar(String message) {
    Get.snackbar(
      'Success',
      message,
      backgroundColor: AppColors.primary4,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
      snackPosition: SnackPosition.TOP,
    );
  }

  void _showErrorSnackbar(String message) {
    Get.snackbar(
      'Error',
      message,
      backgroundColor: AppColors.primary3,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
      snackPosition: SnackPosition.TOP,
    );
  }

  // Getters for UI
  bool get isCreateMode => currentMode.value == BillingMode.create;
  bool get isEditMode => currentMode.value == BillingMode.edit;
  bool get isViewMode => currentMode.value == BillingMode.view;
  bool get isReadOnly => isViewMode;
  
  String get modeTitle {
    switch (currentMode.value) {
      case BillingMode.create:
        return 'Create Billing';
      case BillingMode.edit:
        return 'Edit Billing';
      case BillingMode.view:
        return 'View Billing';
    }
  }
  
  String get submitButtonText {
    if (isLoading.value) return 'Saving...';
    return isEditMode ? 'Update' : 'Save';
  }
}

enum BillingMode { create, edit, view }