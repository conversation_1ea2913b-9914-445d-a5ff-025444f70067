import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/api/api_service.dart';
import 'package:platix/data/models/billing_reports_model.dart';

class BillingController extends GetxController {
  final ApiService _apiService = ApiService();
  final ScrollController scrollController = ScrollController();

  // Observable variables
  var isLoading = false.obs;
  var hasError = false.obs;
  var errorMessage = ''.obs;
  var billingReports = <BillingReportData>[].obs;
  var paginationData =
      PaginationData(total: 0, page: 1, limit: 10, totalPages: 0).obs;

  // Search and pagination
  var searchQuery = ''.obs;
  var currentPage = 1.obs;
  var itemsPerPage = 10.obs;

  @override
  void onInit() {
    super.onInit();
    fetchBillingReports();
    _setupScrollListener();
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }

  void _setupScrollListener() {
    scrollController.addListener(() {
      // Load next page when user is near the end of the list
      if (scrollController.position.extentAfter < 200) {
        if (paginationData.value.hasNextPage && !isLoading.value) {
          loadMoreBillingReports();
        }
      }
    });
  }

  /// Fetch billing reports with pagination and search
  Future<void> fetchBillingReports({bool isRefresh = false}) async {
    try {
      if (isRefresh) {
        currentPage.value = 1;
        billingReports.clear();
      }
      
      isLoading.value = true;
      hasError.value = false;
      errorMessage.value = '';

      final response = await _apiService.getAllBillingReports(
        page: currentPage.value,
        limit: itemsPerPage.value,
        search: searchQuery.value,
      );

      if (isRefresh) {
        billingReports.value = response.data;
      } else {
        billingReports.addAll(response.data);
      }
      
      paginationData.value = response.pagination;
      
      if (response.data.isNotEmpty) {
        currentPage.value++;
      }
      
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      print('Error fetching billing reports: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Load more billing reports for pagination
  Future<void> loadMoreBillingReports() async {
    if (isLoading.value || !paginationData.value.hasNextPage) return;
    await fetchBillingReports();
  }

  /// Search billing reports
  Future<void> searchBillingReports(String query) async {
    searchQuery.value = query;
    currentPage.value = 1;
    await fetchBillingReports(isRefresh: true);
  }

  /// Refresh billing reports
  Future<void> refreshBillingReports() async {
    await fetchBillingReports(isRefresh: true);
  }

  /// Clear search and reset
  Future<void> clearSearch() async {
    searchQuery.value = '';
    currentPage.value = 1;
    await fetchBillingReports(isRefresh: true);
  }

  /// Change items per page
  Future<void> changeItemsPerPage(int newLimit) async {
    itemsPerPage.value = newLimit;
    currentPage.value = 1;
    await fetchBillingReports(isRefresh: true);
  }

  // Getters for UI
  bool get hasData => billingReports.isNotEmpty;
  String get totalItemsText => 'Total: ${paginationData.value.total} items';
  String get currentPageText => 'Page ${paginationData.value.page} of ${paginationData.value.totalPages}';
}
