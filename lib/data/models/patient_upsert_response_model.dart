class PatientUpsertResponse {
  final bool status;
  final String message;
  final PatientUpsertData? data;

  PatientUpsertResponse({
    required this.status,
    required this.message,
    this.data,
  });

  factory PatientUpsertResponse.fromJson(Map<String, dynamic> json) {
    return PatientUpsertResponse(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? PatientUpsertData.fromJson(json['data']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }
}

class PatientUpsertData {
  final String? id;           // Database UUID primary key
  final String? userId;
  final String? firstName;
  final String? lastName;
  final String? patientRegId;
  final String? mobile;
  final String? email;
  final String? age;
  final String? gender;
  final String? country;
  final String? state;
  final String? city;
  final String? address;
  final String? dob;
  final String? name;         // Full name from backend
  final String? createdAt;
  final String? updatedAt;

  PatientUpsertData({
    this.id,
    this.userId,
    this.firstName,
    this.lastName,
    this.patientRegId,
    this.mobile,
    this.email,
    this.age,
    this.gender,
    this.country,
    this.state,
    this.city,
    this.address,
    this.dob,
    this.name,
    this.createdAt,
    this.updatedAt,
  });

  factory PatientUpsertData.fromJson(Map<String, dynamic> json) {
    return PatientUpsertData(
      id: json['id']?.toString(),
      userId: json['user_id']?.toString(),
      firstName: json['first_name']?.toString(),
      lastName: json['last_name']?.toString(),
      patientRegId: json['patient_reg_id']?.toString(),
      mobile: json['mobile']?.toString(),
      email: json['email']?.toString(),
      age: json['age']?.toString(),
      gender: json['gender']?.toString(),
      country: json['country']?.toString(),
      state: json['state']?.toString(),
      city: json['city']?.toString(),
      address: json['address']?.toString(),
      dob: json['dob']?.toString(),
      name: json['name']?.toString(), // Backend provides full name
      createdAt: json['createdAt']?.toString(),
      updatedAt: json['updatedAt']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'first_name': firstName,
      'last_name': lastName,
      'patient_reg_id': patientRegId,
      'mobile': mobile,
      'email': email,
      'age': age,
      'gender': gender,
      'country': country,
      'state': state,
      'city': city,
      'address': address,
      'dob': dob,
      'name': name,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  // Helper method to get display name
  String get displayName => name ?? '${firstName ?? ''} ${lastName ?? ''}'.trim();
}